"use client";

import React from "react";
import PageLayout from "@/components/layout/PageLayout";
import { Link } from "react-router-dom";
import {
  Wrench,
  ChevronLeft
} from "lucide-react";

const Service = () => {
  return (
    <PageLayout hideHero hideBreadcrumbs>
      {/* Top Navigation Bar for External Site */}
      <div className="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side - Back to main site */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.location.href = '/'}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg"
              >
                <ChevronLeft className="h-5 w-5" />
                <span className="font-medium">Back to Atandra Energy</span>
              </button>
            </div>

            {/* Center - Current page indicator */}
            <div className="flex items-center space-x-2 text-gray-700">
              <Wrench className="h-5 w-5" />
              <span className="font-semibold">Support Portal</span>
            </div>

            {/* Right side - Additional navigation */}
            <div className="flex items-center space-x-4">
              <Link
                to="/contact/sales"
                className="text-gray-600 hover:text-gray-900 transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-gray-100"
              >
                Contact Sales
              </Link>
              <Link
                to="/"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium"
              >
                Home
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* External Website Iframe */}
      <div className="h-screen">
        <iframe
          src="http://krykardcare.in/support/#/main"
          className="w-full h-full border-0"
          title="Krykard Support Portal"
          style={{ height: 'calc(100vh - 64px)' }}
        />
      </div>
    </PageLayout>
  );
};

export default Service;